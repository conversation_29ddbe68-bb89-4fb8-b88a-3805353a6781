#!/usr/bin/env python3
"""
Demo Web Crawler - Easy to use web crawler
Just run this script and enter the website URL when prompted
"""

from simple_crawler import SimpleCrawler
import sys

def main():
    print("🕷️  SIMPLE WEB CRAWLER")
    print("=" * 50)
    print("This crawler will download:")
    print("✅ HTML pages")
    print("✅ CSS stylesheets") 
    print("✅ JavaScript files")
    print("✅ Images (PNG, JPG, GIF, SVG)")
    print("✅ Media files (MP4, MP3)")
    print("✅ Other assets (fonts, icons, etc.)")
    print("=" * 50)
    
    # Get URL from user
    if len(sys.argv) > 1:
        url = sys.argv[1]
    else:
        url = input("\n🌐 Enter website URL to crawl: ").strip()
    
    if not url:
        print("❌ No URL provided!")
        return
    
    # Add https if missing
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
    
    print(f"\n🎯 Target: {url}")
    
    # Get max pages
    try:
        max_pages = input("📄 Max pages to crawl (default: 10): ").strip()
        max_pages = int(max_pages) if max_pages else 10
    except ValueError:
        max_pages = 10
    
    print(f"📊 Will crawl maximum {max_pages} pages")
    
    # Start crawling
    print("\n🚀 Starting crawler...")
    try:
        crawler = SimpleCrawler(url)
        crawler.crawl_website(max_pages=max_pages)
        
        print("\n🎉 SUCCESS! Check the output folder for downloaded content.")
        print(f"📁 Output folder: {crawler.output_dir}")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("💡 Make sure the URL is correct and accessible")

if __name__ == "__main__":
    main()
