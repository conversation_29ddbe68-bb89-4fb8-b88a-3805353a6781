#!/usr/bin/env python3
"""
Simple Web Crawler - Crawl any website and download all content
Usage: python simple_crawler.py <URL>
"""

import requests
from bs4 import BeautifulSoup
import os
import urllib.parse
from urllib.parse import urljoin, urlparse
import time
import json
from pathlib import Path
import mimetypes

class SimpleCrawler:
    def __init__(self, base_url, output_dir=None):
        self.base_url = base_url
        self.domain = urlparse(base_url).netloc
        if output_dir is None:
            # T<PERSON>o thư mục riêng cho mỗi domain
            safe_domain = self.domain.replace('.', '_').replace(':', '_')
            output_dir = f"crawled_{safe_domain}"
        self.output_dir = Path(output_dir)
        self.visited_urls = set()
        self.downloaded_files = []
        
        # Create output directories
        self.output_dir.mkdir(exist_ok=True)
        (self.output_dir / "html").mkdir(exist_ok=True)
        (self.output_dir / "css").mkdir(exist_ok=True)
        (self.output_dir / "js").mkdir(exist_ok=True)
        (self.output_dir / "images").mkdir(exist_ok=True)
        (self.output_dir / "media").mkdir(exist_ok=True)
        (self.output_dir / "assets").mkdir(exist_ok=True)
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

    def is_same_domain(self, url):
        """Check if URL belongs to the same domain"""
        return urlparse(url).netloc == self.domain

    def get_file_extension(self, url, content_type=None):
        """Get file extension from URL or content type"""
        parsed = urlparse(url)
        path = parsed.path
        
        if path.endswith('/') or not path:
            return '.html'
        
        _, ext = os.path.splitext(path)
        if ext:
            return ext.lower()
        
        # Try to guess from content type
        if content_type:
            ext = mimetypes.guess_extension(content_type.split(';')[0])
            return ext if ext else '.html'
        
        return '.html'

    def sanitize_filename(self, url):
        """Create a safe filename from URL"""
        parsed = urlparse(url)
        path = parsed.path.strip('/')
        
        if not path:
            path = "index"
        
        # Replace invalid characters
        safe_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_."
        filename = ''.join(c if c in safe_chars else '_' for c in path)
        
        # Limit length
        if len(filename) > 100:
            filename = filename[:100]
        
        return filename

    def download_file(self, url, subfolder="assets"):
        """Download a file and save it to appropriate folder"""
        try:
            if url in self.visited_urls:
                return None
            
            self.visited_urls.add(url)
            
            print(f"Downloading: {url}")
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            content_type = response.headers.get('content-type', '')
            extension = self.get_file_extension(url, content_type)
            filename = self.sanitize_filename(url) + extension
            
            # Determine subfolder based on content type
            if 'text/css' in content_type or url.endswith('.css'):
                subfolder = "css"
            elif 'javascript' in content_type or url.endswith('.js'):
                subfolder = "js"
            elif any(img_type in content_type for img_type in ['image/', 'png', 'jpg', 'jpeg', 'gif', 'svg']):
                subfolder = "images"
            elif any(media_type in content_type for media_type in ['video/', 'audio/', 'mp4', 'mp3']):
                subfolder = "media"
            elif 'text/html' in content_type:
                subfolder = "html"
            
            file_path = self.output_dir / subfolder / filename
            
            # Handle duplicate filenames
            counter = 1
            original_path = file_path
            while file_path.exists():
                name_part = original_path.stem
                ext_part = original_path.suffix
                file_path = original_path.parent / f"{name_part}_{counter}{ext_part}"
                counter += 1
            
            with open(file_path, 'wb') as f:
                f.write(response.content)
            
            self.downloaded_files.append({
                'url': url,
                'local_path': str(file_path),
                'content_type': content_type,
                'size': len(response.content)
            })
            
            print(f"Saved: {file_path}")
            return str(file_path)
            
        except Exception as e:
            print(f"Error downloading {url}: {e}")
            return None

    def extract_links_from_html(self, html_content, base_url):
        """Extract all links from HTML content"""
        soup = BeautifulSoup(html_content, 'html.parser')
        links = set()
        
        # Extract different types of links
        selectors = [
            ('a', 'href'),           # Links
            ('link', 'href'),        # CSS, icons, etc.
            ('script', 'src'),       # JavaScript
            ('img', 'src'),          # Images
            ('video', 'src'),        # Videos
            ('audio', 'src'),        # Audio
            ('source', 'src'),       # Media sources
            ('iframe', 'src'),       # Iframes
        ]
        
        for tag_name, attr_name in selectors:
            for tag in soup.find_all(tag_name):
                url = tag.get(attr_name)
                if url:
                    absolute_url = urljoin(base_url, url)
                    links.add(absolute_url)
        
        # Extract CSS background images
        for tag in soup.find_all(style=True):
            style = tag.get('style', '')
            if 'background-image' in style or 'url(' in style:
                # Simple regex to extract URLs from CSS
                import re
                urls = re.findall(r'url\(["\']?([^"\']+)["\']?\)', style)
                for url in urls:
                    absolute_url = urljoin(base_url, url)
                    links.add(absolute_url)
        
        return links

    def crawl_page(self, url):
        """Crawl a single page and extract all resources"""
        try:
            print(f"\nCrawling page: {url}")
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            # Save HTML content
            html_filename = self.sanitize_filename(url) + '.html'
            html_path = self.output_dir / "html" / html_filename
            
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            self.downloaded_files.append({
                'url': url,
                'local_path': str(html_path),
                'content_type': 'text/html',
                'size': len(response.text)
            })
            
            # Extract all links from the page
            links = self.extract_links_from_html(response.text, url)
            
            # Download all assets
            for link in links:
                if self.is_same_domain(link) or not self.is_same_domain(url):
                    # Download CSS, JS, images, and other assets
                    parsed = urlparse(link)
                    if parsed.path:
                        ext = os.path.splitext(parsed.path)[1].lower()
                        if ext in ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', 
                                  '.woff', '.woff2', '.ttf', '.eot', '.mp4', '.mp3', '.pdf']:
                            self.download_file(link)
            
            # Return HTML links for further crawling
            html_links = []
            for link in links:
                if self.is_same_domain(link):
                    parsed = urlparse(link)
                    if not parsed.path or parsed.path.endswith('/') or parsed.path.endswith('.html'):
                        html_links.append(link)
            
            return html_links
            
        except Exception as e:
            print(f"Error crawling {url}: {e}")
            return []

    def crawl_website(self, max_pages=50):
        """Crawl the entire website"""
        print(f"Starting to crawl: {self.base_url}")
        print(f"Output directory: {self.output_dir}")
        
        urls_to_visit = [self.base_url]
        pages_crawled = 0
        
        while urls_to_visit and pages_crawled < max_pages:
            current_url = urls_to_visit.pop(0)
            
            if current_url not in self.visited_urls:
                self.visited_urls.add(current_url)
                new_links = self.crawl_page(current_url)
                
                # Add new links to visit
                for link in new_links:
                    if link not in self.visited_urls and link not in urls_to_visit:
                        urls_to_visit.append(link)
                
                pages_crawled += 1
                time.sleep(1)  # Be polite to the server
        
        # Save crawl report
        report = {
            'base_url': self.base_url,
            'pages_crawled': pages_crawled,
            'total_files': len(self.downloaded_files),
            'files': self.downloaded_files
        }
        
        with open(self.output_dir / 'crawl_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Crawling completed!")
        print(f"📄 Pages crawled: {pages_crawled}")
        print(f"📁 Total files downloaded: {len(self.downloaded_files)}")
        print(f"📊 Report saved: {self.output_dir / 'crawl_report.json'}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python simple_crawler.py <URL>")
        print("Example: python simple_crawler.py https://example.com")
        sys.exit(1)
    
    url = sys.argv[1]
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
    
    crawler = SimpleCrawler(url)
    crawler.crawl_website()
